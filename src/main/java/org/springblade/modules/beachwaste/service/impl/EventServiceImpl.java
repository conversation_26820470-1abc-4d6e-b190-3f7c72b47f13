package org.springblade.modules.beachwaste.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Point;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.beachwaste.enums.EventStatusEnum;
import org.springblade.modules.beachwaste.excel.EventExcel;
import org.springblade.modules.beachwaste.excel.EventExportExcel;
import org.springblade.modules.beachwaste.mapper.EventMapper;
import org.springblade.modules.beachwaste.pojo.dto.EventProcessDTO;
import org.springblade.modules.beachwaste.pojo.dto.EventQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.GeoJsonFeature;
import org.springblade.modules.beachwaste.pojo.dto.GeoJsonFeatureCollection;
import org.springblade.modules.beachwaste.pojo.dto.GridEventQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.MapEventQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.PointGeometry;
import org.springblade.modules.beachwaste.pojo.dto.StaffEventQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.pojo.entity.SpatialGrid;
import org.springblade.modules.beachwaste.pojo.vo.*;
import org.springblade.modules.beachwaste.service.IEventService;
import org.springblade.modules.beachwaste.service.ISpatialGridService;
import org.springblade.modules.beachwaste.util.*;
import org.springblade.modules.fh.service.IFhMinioService;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.service.ISystemConfigService;
import org.springblade.modules.system.service.IUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 事件信息服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class EventServiceImpl extends ServiceImpl<EventMapper, Event> implements IEventService {

    private ISpatialGridService spatialGridService;
	private IUserService userService;
	private IFhMinioService fhMinioService;
	private ISystemConfigService systemConfigService;
	private EventMapper eventMapper;
	private BeachWasteMinioClientUtil beachWasteMinioClientUtil;
	private final ThumbnailProcessor thumbnailProcessor;

    /**
     * 将事件对象转换为事件详情VO对象（单个事件，存在N+1查询问题，建议使用批量版本）
     * @param event 事件对象
     * @return 事件详情VO对象
     */
    private EventDetailVO convertToEventDetailVO(Event event) {
        if (event == null) {
            log.warn("尝试转换空的事件对象为EventDetailVO");
            return null;
        }

        try {
            // 为单个事件创建空的映射，会触发数据库查询
            Map<Long, String> gridNameMap = new HashMap<>(1);
            Map<Long, String> userNameMap = new HashMap<>(1);

            // 获取网格信息
            if (event.getGridId() != null) {
                try {
                    SpatialGrid grid = spatialGridService.getById(event.getGridId());
                    if (grid != null && StringUtils.hasText(grid.getGridName())) {
                        gridNameMap.put(grid.getId(), grid.getGridName());
                    }
                } catch (Exception e) {
                    log.error("获取网格信息失败，网格ID: {}, 错误: {}", event.getGridId(), e.getMessage());
                }
            }

            // 获取用户信息
            if (event.getHandlerStaffId() != null) {
                try {
                    User user = userService.getById(event.getHandlerStaffId());
                    if (user != null && StringUtils.hasText(user.getRealName())) {
                        userNameMap.put(user.getId(), user.getRealName());
                    }
                } catch (Exception e) {
                    log.error("获取用户信息失败，用户ID: {}, 错误: {}", event.getHandlerStaffId(), e.getMessage());
                }
            }

            return convertToEventDetailVO(event, gridNameMap, userNameMap);
        } catch (Exception e) {
            log.error("转换事件详情失败，事件ID: {}, 错误: {}", event.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将事件对象转换为事件详情VO对象（批量优化版本）
     * @param event 事件对象
     * @param gridNameMap 网格名称映射
     * @param userNameMap 用户名称映射
     * @return 事件详情VO对象
     */
    private EventDetailVO convertToEventDetailVO(Event event, Map<Long, String> gridNameMap, Map<Long, String> userNameMap) {
        if (event == null) {
            return null;
        }

        // 转换为VO对象
        EventDetailVO vo = new EventDetailVO();
        BeanUtils.copyProperties(event, vo, "location");

        // 使用工具类安全地转换Point对象为位置字符串
        Point location = event.getLocation();
        vo.setLocation(LocationSerializationUtil.pointToString(location));

        vo.setDiscoveryTime(event.getDiscoveryTime());

        // 设置枚举描述
        vo.setWasteSizeDesc(EventUtil.getWasteSizeDescription(event.getWasteSize()));
        vo.setWasteMaterialDesc(EventUtil.getWasteMaterialDescription(event.getWasteMaterial()));
        vo.setDiscoveryMethodDesc(EventUtil.getDiscoveryMethodDescription(event.getDiscoveryMethod()));

        // 设置事件标题
        vo.setEventTitle(EventUtil.buildEventTitle(event));

        // 从映射中获取网格名称
        String gridName = "";
        if (event.getGridId() != null) {
            gridName = gridNameMap.getOrDefault(event.getGridId(), "未知网格");
        } else {
            gridName = "未分配网格";
        }
        vo.setGridName(gridName);

        // 从映射中获取处理网格员名称
        String handlerStaffName = "";
        if (event.getHandlerStaffId() != null) {
            handlerStaffName = userNameMap.getOrDefault(event.getHandlerStaffId(), "未知网格员");
        } else {
            handlerStaffName = "未分配";
        }
        vo.setHandlerStaffName(handlerStaffName);

        // 将discoveryImagePath从ObjectKey转换为真实URL
        if (StringUtils.hasText(event.getDiscoveryImagePath())) {
            String realImageUrl = fhMinioService.getObjectUrl(event.getDiscoveryImagePath());
            vo.setDiscoveryImagePath(realImageUrl);
        }

        // 把processedImagePath中的值取出来（miniO存储的ObjectKey），转换为真实URL，再赋值给vo对象
        if (StringUtils.hasText(event.getProcessedImagePath())) {
            try {
                // 使用BeachWasteMinioClientUtil获取对象URL
                String realImageUrl = beachWasteMinioClientUtil.getObjectUrl(event.getProcessedImagePath());
                vo.setProcessedImagePath(realImageUrl);
            } catch (Exception e) {
                log.error("获取处理图片URL失败: {}", e.getMessage());
            }
        }

        return vo;
    }

    /**
     * 将事件对象转换为事件滚动VO对象
     * @param event 事件对象
     * @return 事件滚动VO对象
     */
    private EventScrollVO convertToEventScrollVO(Event event) {
        if (event == null) {
            return null;
        }

        EventScrollVO vo = new EventScrollVO();

        // 格式化日期为 yyyy/MM/dd
        vo.setDiscoveryTime(event.getDiscoveryTime());

        // 设置垃圾材质与尺寸信息
        vo.setWasteInfo(EventUtil.buildEventTitle(event));

        // 设置发现方式和事件状态
        vo.setDiscoveryMethod(EventUtil.getDiscoveryMethodDescription(event.getDiscoveryMethod()));
        vo.setEventStatus(EventUtil.getEventStatusDescription(event.getEventStatus()));

        return vo;
    }

    /**
     * 将事件对象转换为网格事件VO对象
     * @param event 事件对象
     * @param grid 网格对象
     * @return 网格事件VO对象
     */
    private GridEventVO convertToGridEventVO(Event event, SpatialGrid grid) {
        if (event == null) {
            return null;
        }

        GridEventVO vo = new GridEventVO();
        // 使用BeanUtils复制基础属性 (id)
        BeanUtils.copyProperties(event, vo);

        // 事件标题
        vo.setTitle(EventUtil.buildEventTitle(event));
        // 垃圾尺寸描述
        vo.setWasteSize(EventUtil.getWasteSizeDescription(event.getWasteSize()));
        // 事件状态描述
        vo.setEventStatus(EventUtil.getEventStatusDescription(event.getEventStatus()));
        // 格式化发现时间
        vo.setDiscoveryTime(event.getDiscoveryTime());
        // 发现方式描述
        vo.setDiscoveryMethod(EventUtil.getDiscoveryMethodDescription(event.getDiscoveryMethod()));
        // 格式化置信度
        if (event.getConfidence() != null) {
            vo.setConfidence(EventUtil.formatConfidence(event.getConfidence().doubleValue()));
        } else {
            vo.setConfidence(EventUtil.formatConfidence(0.0));
        }

        // 将processedImagePath从ObjectKey转换为真实URL
        if (StringUtils.hasText(event.getProcessedImagePath())) {
            String realImageUrl = fhMinioService.getObjectUrl(event.getProcessedImagePath());
            vo.setProcessedImagePath(realImageUrl);
        }

        return vo;
    }

    /**
     * 将事件对象转换为事件位置VO对象
     * @param event 事件对象
     * @return 事件位置VO对象
     */
    private EventLocationVO convertToEventLocationVO(Event event) {
        if (event == null || event.getLocation() == null || event.getLocation().isEmpty()) {
            return null;
        }

        // 创建一个新的EventLocationVO对象
        EventLocationVO vo = new EventLocationVO();
        // 设置事件ID
        vo.setId(event.getId());

        try {
            // 使用工具类从JTS Point对象中提取经纬度信息
            Point location = event.getLocation();
            if (LocationSerializationUtil.isValidPoint(location)) {
                double longitude = LocationSerializationUtil.getLongitude(location);
                double latitude = LocationSerializationUtil.getLatitude(location);

                // 设置经度和纬度，格式化为字符串
                vo.setLongitude(String.valueOf(longitude));
                vo.setLatitude(String.valueOf(latitude));
            } else {
                vo.setLongitude("0.0");
                vo.setLatitude("0.0");
            }

        } catch (Exception e) {
            log.error("解析事件位置信息失败，事件ID: {}, 错误: {}", event.getId(), e.getMessage());
            return null;
        }

        return vo;
    }

    /**
     * 将JTS Point对象转换为位置字符串
     * @param point JTS Point对象
     * @return 位置字符串，格式为 "经度,纬度"
     */
    private String convertPointToLocationString(Point point) {
        return LocationSerializationUtil.pointToString(point);
    }

    @Override
    public EventDetailVO getEventDetailById(Long id) {
        // 获取事件基本信息
        Event event = this.getById(id);
        return convertToEventDetailVO(event);
    }

    @Override
    public R checkToSave(Event event) {
        boolean success = this.save(event);
        return R.status(success);
    }

    @Override
    public R checkToUpdateById(Event event) {
        return R.status(this.updateById(event));
    }

    @Override
    public R getWasteMaterialStats(Integer timeRange) {
        // 计算时间范围
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime;

        switch (timeRange) {
			// 近一周
            case 1:
                startTime = endTime.minusWeeks(1);
                break;
			// 近一月
            case 2:
                startTime = endTime.minusMonths(1);
                break;
			// 近一年
            case 3:
                startTime = endTime.minusYears(1);
                break;
            default:
                return R.fail("无效的时间范围参数");
        }

        // 创建条件构造器
        Date startDate = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant());
        LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<Event>()
            .ge(Event::getDiscoveryTime, startDate)
            .le(Event::getDiscoveryTime, endDate);

        List<Event> eventList = this.list(queryWrapper);

        // 统计各类垃圾材质的数量并转换为前端需要的格式
        List<Map<String, Object>> result = eventList.stream()
                .collect(Collectors.groupingBy(Event::getWasteMaterial, Collectors.counting()))
                .entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>(4);
                    item.put("material", EventUtil.getWasteMaterialDescription(entry.getKey()));
                    item.put("count", entry.getValue());
                    return item;
                })
                .collect(Collectors.toList());

        return R.data(result);
    }

    @Override
    public R listByCondition(Long discoveryMethod, Long eventStatus, Long gridId, String startDate, String endDate) {
        // 创建条件构造器
        LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<Event>()
            .eq(Objects.nonNull(discoveryMethod), Event::getDiscoveryMethod, discoveryMethod)
            .eq(Objects.nonNull(eventStatus), Event::getEventStatus, eventStatus)
            .eq(Objects.nonNull(gridId), Event::getGridId, gridId);

        // 处理开始日期
        if (StringUtils.hasText(startDate)) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate start = LocalDate.parse(startDate, formatter);
                LocalDateTime startDateTime = start.atTime(LocalTime.MIN);
                queryWrapper.ge(Event::getDiscoveryTime, Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            } catch (Exception e) {
                log.error("开始日期格式错误: {}", startDate, e);
            }
        }

        // 处理结束日期
        if (StringUtils.hasText(endDate)) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate end = LocalDate.parse(endDate, formatter);
                LocalDateTime endDateTime = end.atTime(LocalTime.MAX);
                queryWrapper.le(Event::getDiscoveryTime, Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            } catch (Exception e) {
                log.error("结束日期格式错误: {}", endDate, e);
            }
        }

        queryWrapper.orderByDesc(Event::getDiscoveryTime);

        List<Event> eventList = this.list(queryWrapper);

        // 转换为VO
        List<EventScrollVO> resList = eventList.stream()
            .map(this::convertToEventScrollVO)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        return R.data(resList);
    }

    @Override
    public void exportByCondition(Long discoveryMethod, Long eventStatus, Long gridId,
								  String startDate, String endDate, HttpServletResponse response) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<Event> queryWrapper = EventQueryUtil.buildEventQueryWrapper(
				discoveryMethod, eventStatus, gridId, startDate, endDate);

            // 使用统一的分页导出方法
            List<EventExportExcel> excelDataList = exportEventsByQueryWrapper(queryWrapper);

            // 导出Excel
            ExcelUtil.export(response, "事件列表", "事件列表", excelDataList, EventExportExcel.class);
            log.info("成功导出事件列表，共{}条记录", excelDataList.size());
        } catch (Exception e) {
            log.error("导出事件列表失败", e);
            throw new RuntimeException("导出事件列表失败: " + e.getMessage());
        }
    }

    /**
     * 使用分页查询导出事件数据
     * @param queryWrapper 查询条件
     * @return Excel数据列表
     */
    private List<EventExportExcel> exportEventsByQueryWrapper(LambdaQueryWrapper<Event> queryWrapper) {
        // 使用批量处理工具类进行分页处理
        return EventBatchUtil.processWithPagination(
            queryWrapper,
            this,
            eventList -> {
                // 批量获取关联数据
                EventBatchUtil.BatchDataMaps batchDataMaps = EventBatchUtil.getBatchDataMaps(eventList, spatialGridService, userService);
                // 转换为Excel对象
                return EventUtil.convertToEventExportExcelList(eventList, batchDataMaps.getGridNameMap(), batchDataMaps.getUserNameMap());
            }
        );
    }

    @Override
    public void exportEventExcel(Long[] ids, HttpServletResponse response) {
        try {
            if (ids == null || ids.length == 0) {
                throw new IllegalArgumentException("导出事件ID列表不能为空");
            }

            // 批量获取事件信息，避免N+1查询问题
            List<Event> eventList = this.listByIds(Arrays.asList(ids));
            if (eventList.isEmpty()) {
                throw new IllegalArgumentException("未找到指定的事件记录");
            }

            // 使用统一的批量数据获取和转换方法
            EventBatchUtil.BatchDataMaps batchMaps = EventBatchUtil.getBatchDataMaps(eventList, spatialGridService, userService);
            List<EventExcel> excelDataList = EventUtil.convertToEventExcelList(eventList, batchMaps.getGridNameMap(), batchMaps.getUserNameMap());

            // 导出Excel
            ExcelUtil.export(response, "事件详情", "事件详情", excelDataList, EventExcel.class);
        } catch (Exception e) {
            log.error("导出事件详情失败", e);
            throw new RuntimeException("导出事件详情失败: " + e.getMessage());
        }
    }

    @Override
    public R getEventYears() {
        // 直接从数据库获取年份列表（已按降序排序）
        return R.data(baseMapper.selectEventYears());
    }

    @Override
    public R getMonthlyStats(Integer year) {
        // 通过数据库层直接分组统计
        List<Map<String, Object>> dbResult = baseMapper.selectMonthlyStatsByYear(year);

        // 将数据库结果转换为Map
        Map<Integer, Long> monthlyStats = new HashMap<>();
        for (Map<String, Object> entry : dbResult) {
            int month = (Integer) entry.get("month");
            long count = (Long) entry.get("count");
            monthlyStats.put(month, count);
        }

        // 补全12个月份的数据，没有数据的月份设为0
        List<Map<String, Object>> result = new ArrayList<>();
        for (int month = 1; month <= 12; month++) {
            Map<String, Object> monthData = new HashMap<>();
            monthData.put("month", month);
            monthData.put("count", monthlyStats.getOrDefault(month, 0L));
            result.add(monthData);
        }

        return R.data(result);
    }

	@Override
	public R getEventsByGridId(GridEventQueryDTO queryDTO) {
		// 验证网格是否存在
		SpatialGrid grid = spatialGridService.getById(queryDTO.getGridId());
		if (grid == null) {
			return R.fail("网格不存在");
		}

		// 如果时间范围为空，默认查询最近一个月
		if (!StringUtils.hasText(queryDTO.getStartDate()) && !StringUtils.hasText(queryDTO.getEndDate())) {
			LocalDateTime now = LocalDateTime.now();
			DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
			queryDTO.setEndDate(now.format(dateFormatter));
			queryDTO.setStartDate(now.minusMonths(1).format(dateFormatter));
		}

		// 创建条件构造器
		LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<Event>()
			.eq(Event::getGridId, queryDTO.getGridId())
			.eq(Objects.nonNull(queryDTO.getEventStatus()), Event::getEventStatus, queryDTO.getEventStatus())
			.eq(Objects.nonNull(queryDTO.getDiscoveryMethod()), Event::getDiscoveryMethod, queryDTO.getDiscoveryMethod())
			.gt(Objects.nonNull(queryDTO.getConfidence()), Event::getConfidence, queryDTO.getConfidence())
			.orderByDesc(Event::getDiscoveryTime);

		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		// 处理开始日期
		if (StringUtils.hasText(queryDTO.getStartDate())) {
			try {
				LocalDate start = LocalDate.parse(queryDTO.getStartDate(), formatter);
				LocalDateTime startDateTime = start.atTime(LocalTime.MIN);
				queryWrapper.ge(Event::getDiscoveryTime, Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant()));
			} catch (Exception e) {
				log.error("开始日期格式错误: {}", queryDTO.getStartDate(), e);
			}
		}

		// 处理结束日期
		if (StringUtils.hasText(queryDTO.getEndDate())) {
			try {
				LocalDate end = LocalDate.parse(queryDTO.getEndDate(), formatter);
				LocalDateTime endDateTime = end.atTime(LocalTime.MAX);
				queryWrapper.le(Event::getDiscoveryTime, Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant()));
			} catch (Exception e) {
				log.error("结束日期格式错误: {}", queryDTO.getEndDate(), e);
			}
		}

		List<Event> eventList = this.list(queryWrapper);

		// 转换为前端需要的格式
		List<GridEventVO> resultList = eventList.stream()
            .map(event -> convertToGridEventVO(event, grid))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

		return R.data(resultList);
	}

    @Override
    public List<EventLocationVO> getEventLocationsByDateRange(String startDate, String endDate) {
        // 创建条件构造器
        LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<Event>()
            // 排除误报事件
            .ne(Event::getEventStatus, EventStatusEnum.FALSE_ALARM.getId())
            .isNotNull(Event::getLocation)
            .ne(Event::getLocation, "");

        // 处理开始日期
        if (StringUtils.hasText(startDate)) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate start = LocalDate.parse(startDate, formatter);
                LocalDateTime startDateTime = start.atTime(LocalTime.MIN);
                queryWrapper.ge(Event::getDiscoveryTime, Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            } catch (Exception e) {
                log.error("开始日期格式错误: {}", startDate, e);
            }
        }

        // 处理结束日期
        if (StringUtils.hasText(endDate)) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate end = LocalDate.parse(endDate, formatter);
                LocalDateTime endDateTime = end.atTime(LocalTime.MAX);
                queryWrapper.le(Event::getDiscoveryTime, Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            } catch (Exception e) {
                log.error("结束日期格式错误: {}", endDate, e);
            }
        }

        // 查询事件列表
        List<Event> eventList = this.list(queryWrapper);

        // 转换为位置VO列表
        return eventList.stream()
            .map(this::convertToEventLocationVO)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 构建事件查询条件
     *
     * @param queryDTO 查询条件DTO
     * @return 构建好的查询条件包装器
     */
    private LambdaQueryWrapper<Event> buildQueryWrapper(EventQueryDTO queryDTO) {
        // 创建条件构造器
        LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<Event>()
            .eq(Objects.nonNull(queryDTO.getDiscoveryMethod()), Event::getDiscoveryMethod, queryDTO.getDiscoveryMethod())
            .eq(Objects.nonNull(queryDTO.getEventStatus()), Event::getEventStatus, queryDTO.getEventStatus())
            .eq(Objects.nonNull(queryDTO.getWasteMaterial()), Event::getWasteMaterial, queryDTO.getWasteMaterial())
            .eq(Objects.nonNull(queryDTO.getWasteSize()), Event::getWasteSize, queryDTO.getWasteSize())
            .eq(Objects.nonNull(queryDTO.getGridId()), Event::getGridId, queryDTO.getGridId())
            .eq(Objects.nonNull(queryDTO.getHandlerStaffId()), Event::getHandlerStaffId, queryDTO.getHandlerStaffId())
            .orderByDesc(Event::getDiscoveryTime);

        // 处理日期范围查询
        if (queryDTO.getStartDate() != null) {
            LocalDateTime startDateTime = queryDTO.getStartDate().atStartOfDay();
            queryWrapper.ge(Event::getDiscoveryTime, Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (queryDTO.getEndDate() != null) {
            LocalDateTime endDateTime = queryDTO.getEndDate().atTime(LocalTime.MAX);
            queryWrapper.le(Event::getDiscoveryTime, Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        }

        return queryWrapper;
    }

    @Override
    public R listByQueryDTO(EventQueryDTO queryDTO) {
        // 获取查询条件
        LambdaQueryWrapper<Event> queryWrapper = buildQueryWrapper(queryDTO);

        // 执行分页查询
        Page<Event> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        Page<Event> eventPage = this.page(page, queryWrapper);

        // 转换为VO
        List<EventDetailVO> resList = eventPage.getRecords().stream()
            .map(this::convertToEventDetailVO)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

		if (queryDTO.getLoadPreview()) {
			// 异步并行处理每个事件的发现图片，根据bbox生成缩略图
			processEventThumbnailsAsync(resList);
		}

        // 构建分页结果
        Page<EventDetailVO> resultPage = new Page<>();
        resultPage.setRecords(resList);
        resultPage.setTotal(eventPage.getTotal());
        resultPage.setCurrent(eventPage.getCurrent());
        resultPage.setSize(eventPage.getSize());

        return R.data(resultPage);
    }

        @Override
        public R listAllByQueryDTO(EventQueryDTO queryDTO) {
            log.info("执行全量查询，查询条件: {}", queryDTO);

            // 获取查询条件
            LambdaQueryWrapper<Event> queryWrapper = buildQueryWrapper(queryDTO);

            List<Event> eventList = this.list(queryWrapper);

            // 使用批量处理避免N+1查询问题
            List<EventDetailVO> resList;
            if (eventList.isEmpty()) {
                resList = new ArrayList<>();
            } else {
                // 批量获取关联数据（网格和用户信息）
                EventBatchUtil.BatchDataMaps batchDataMaps = EventBatchUtil.getBatchDataMaps(
                    eventList, spatialGridService, userService);

                // 使用批量转换方法，避免单个查询
                resList = eventList.stream()
                    .map(event -> convertToEventDetailVO(event,
                        batchDataMaps.getGridNameMap(),
                        batchDataMaps.getUserNameMap()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            }

            return R.data(resList);
        }

    @Override
    public R getProcessedStaffStats(Integer year) {
        // 参数校验
        if (year == null) {
            return R.fail("年份参数不能为空");
        }

        // 查询指定年份已处置事件的处理人员统计
        // 使用EventStatusEnum.PROCESSED.getId()确保只统计已处置状态的事件
        List<ProcessedStaffStatsVO> staffStats = baseMapper.selectProcessedStaffStatsByYear(year, EventStatusEnum.PROCESSED.getId());

        // 如果没有数据，返回空列表
        if (staffStats == null || staffStats.isEmpty()) {
            // 返回空的两个数组
            Map<String, Object> emptyResult = new HashMap<>();
            emptyResult.put("namelist", new ArrayList<String>());
            emptyResult.put("datalist", new ArrayList<Integer>());
            return R.data(emptyResult);
        }

        // 将数据转换为前端需要的格式：两个数组，一个存放人员姓名，一个存放对应的处理事件数量
        List<String> nameList = new ArrayList<>();
        List<Integer> dataList = new ArrayList<>();

        // 提取人员姓名和处理事件数量
        for (ProcessedStaffStatsVO stat : staffStats) {
            nameList.add(stat.getStaffName());
            dataList.add(stat.getEventCount());
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("namelist", nameList);
        result.put("datalist", dataList);

        return R.data(result);
    }

    @Override
    public R getEventsByStaffId(StaffEventQueryDTO queryDTO) {
        // 获取当前登录用户ID
        Long currentStaffId = AuthUtil.getUserId();
        if (currentStaffId == null) {
            return R.fail("获取当前用户信息失败");
        }

		// 创建条件构造器
		LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<Event>()
			.eq(Event::getHandlerStaffId, currentStaffId)
			.eq(Objects.nonNull(queryDTO.getEventStatus()), Event::getEventStatus, queryDTO.getEventStatus());

		// 根据eventStatus参数设置排序规则
		if (Objects.isNull(queryDTO.getEventStatus())) {
			// 当不传eventStatus时，先按照更新时间，再按照发现时间排序
			queryWrapper.orderByDesc(Event::getUpdateTime)
						.orderByDesc(Event::getDiscoveryTime);
		} else {
			// 当传入具体参数时，按照最近发现时间排序
			queryWrapper.orderByDesc(Event::getDiscoveryTime);
		}

		// 添加日期范围条件
		if (StringUtil.isNotBlank(queryDTO.getStartDate())) {
			LocalDate startDate = LocalDate.parse(queryDTO.getStartDate());
			LocalDateTime startDateTime = startDate.atStartOfDay();
			queryWrapper.ge(Event::getDiscoveryTime, Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant()));
		}
		if (StringUtil.isNotBlank(queryDTO.getEndDate())) {
			LocalDate endDate = LocalDate.parse(queryDTO.getEndDate());
			LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);
			queryWrapper.le(Event::getDiscoveryTime, Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant()));
		}

        // 执行分页查询
        Page<Event> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        Page<Event> eventPage = this.page(page, queryWrapper);

        // 转换为VO
        List<EventDetailVO> resList = eventPage.getRecords().stream()
            .map(this::convertToEventDetailVO)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        // 异步并行处理每个事件的发现图片，根据bbox生成缩略图
        processEventThumbnailsAsync(resList);

        // 构建分页结果
        Page<EventDetailVO> resultPage = new Page<>();
        resultPage.setRecords(resList);
        resultPage.setTotal(eventPage.getTotal());
        resultPage.setCurrent(eventPage.getCurrent());
        resultPage.setSize(eventPage.getSize());

        return R.data(resultPage);
    }

    @Override
    public void exportEventsByDateRange(String startDate, String endDate, HttpServletResponse response) {
        try {
            // 构建日期范围查询条件
            LambdaQueryWrapper<Event> queryWrapper = EventQueryUtil.buildDateRangeQueryWrapper(startDate, endDate);

            // 查询事件列表
            List<Event> eventList = this.list(queryWrapper);

            if (eventList.isEmpty()) {
                // 如果没有数据，导出空的Excel
                ExcelUtil.export(response, "事件详情", "事件详情", new ArrayList<>(), EventExcel.class);
                return;
            }

            // 使用统一的批量数据获取和转换方法
            EventBatchUtil.BatchDataMaps batchMaps = EventBatchUtil.getBatchDataMaps(eventList, spatialGridService, userService);
            List<EventExcel> excelDataList = EventUtil.convertToEventExcelList(eventList, batchMaps.getGridNameMap(), batchMaps.getUserNameMap());

            // 生成文件名
            String fileName = String.format("事件数据_%s至%s", startDate, endDate);

            // 导出Excel
            ExcelUtil.export(response, fileName, "事件数据", excelDataList, EventExcel.class);
        } catch (Exception e) {
            log.error("根据时间范围导出事件失败", e);
            throw new RuntimeException("根据时间范围导出事件失败: " + e.getMessage());
        }
    }

    @Override
    public R processEvent(EventProcessDTO processDTO) {
        try {
            // 1. 参数校验
            if (processDTO.getEventId() == null) {
                return R.fail("事件ID不能为空");
            }

            // 2. 获取事件信息
            Event event = this.getById(processDTO.getEventId());
            if (event == null) {
                return R.fail("事件不存在");
            }

            // 确保事件状态为 PENDING (0)
            if (!EventStatusEnum.PENDING.getId().equals(event.getEventStatus())) {
                return R.fail("只能处理待处理状态的事件");
            }

            // 3. 验证状态参数
            Long status = processDTO.getStatus();
            if (!isValidProcessStatus(status)) {
                return R.fail("处置状态无效，只能为：2-已处理，3-误报，4-未找到");
            }

            // 4. 验证距离
            String eventLocationStr = convertPointToLocationString(event.getLocation());
            if (!LocationValidationUtil.validateDistance(eventLocationStr,
                     processDTO.getLongitude(), processDTO.getLatitude(), systemConfigService)) {
                 return R.fail("当前位置距离事件位置超过限制范围，无法处置");
             }

             // 5. 处理图片ObjectKey（仅当状态为已处理时）
             String processedImageObjectKey = null;
             if (EventStatusEnum.PROCESSED.getId().equals(status)) {
                 if (processDTO.getProcessedImageObjectKey() == null || processDTO.getProcessedImageObjectKey().trim().isEmpty()) {
                     return R.fail("已处理状态必须提供处置后的图片ObjectKey");
                 }
                 processedImageObjectKey = processDTO.getProcessedImageObjectKey().trim();
             }

            // 6. 更新事件信息
            Event updateEvent = new Event();
            updateEvent.setId(processDTO.getEventId());
            updateEvent.setEventStatus(status);
            updateEvent.setHandlerReportTime(new Date());
            updateEvent.setHandlerStaffId(AuthUtil.getUserId());

            if (processedImageObjectKey != null) {
                 updateEvent.setProcessedImagePath(processedImageObjectKey);
             }

            boolean updateResult = this.updateById(updateEvent);
            if (!updateResult) {
                return R.fail("事件处置失败");
            }

            return R.success("事件处置成功");

        } catch (Exception e) {
            log.error("事件处置异常", e);
            return R.fail("事件处置失败：" + e.getMessage());
        }
    }

    /**
      * 验证处置状态是否有效
      * @param status 状态值
      * @return 是否有效
      */
     private boolean isValidProcessStatus(Long status) {
         return EventStatusEnum.PROCESSED.getId().equals(status) ||
                EventStatusEnum.FALSE_ALARM.getId().equals(status) ||
                status.equals(4L);
     }

    /**
     * 获取当前登录用户的事件处理统计数据
     * @return 用户事件统计结果
     */
    @Override
    public R getUserEventStats() {
        try {
            // 获取当前登录用户ID
            Long userId = AuthUtil.getUserId();
            if (userId == null) {
                return R.fail("用户未登录");
            }

            // 查询累计处理事件数量
            Integer totalEvents = eventMapper.selectTotalEventsByUserId(userId);
            if (totalEvents == null) {
                totalEvents = 0;
            }

            // 查询当前月处理事件数量
            Integer monthlyEvents = eventMapper.selectMonthlyEventsByUserId(userId);
            if (monthlyEvents == null) {
                monthlyEvents = 0;
            }

            // 构建返回对象
            UserEventStatsVO statsVO = new UserEventStatsVO();
            statsVO.setTotalEvents(totalEvents);
            statsVO.setMonthlyEvents(monthlyEvents);

            return R.data(statsVO);

        } catch (Exception e) {
            log.error("获取用户事件统计数据异常", e);
            return R.fail("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 异步并行处理事件缩略图生成
     * 使用CompletableFuture提高处理效率
     *
     * @param eventDetailVOList 事件详情VO对象列表
     */
    private void processEventThumbnailsAsync(List<EventDetailVO> eventDetailVOList) {
        if (eventDetailVOList == null || eventDetailVOList.isEmpty()) {
            log.debug("事件列表为空，跳过缩略图处理");
            return;
        }

        try {
            // 创建异步任务列表
            List<CompletableFuture<Void>> futures = eventDetailVOList.stream()
                    .map(this::processEventThumbnailAsync)
                    .collect(Collectors.toList());

            // 等待所有异步任务完成，设置超时时间防止长时间阻塞
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            // 设置30秒超时，避免长时间等待
            allFutures.get(30, TimeUnit.SECONDS);

            log.debug("批量缩略图处理完成，共处理 {} 个事件", eventDetailVOList.size());

        } catch (Exception e) {
            log.error("批量处理事件缩略图时发生异常，错误信息: {}", e.getMessage(), e);
            // 异常情况下不影响主流程，继续返回原始数据
        }
    }

    /**
     * 异步处理单个事件的缩略图生成
     *
     * @param eventDetailVO 事件详情VO对象
     * @return CompletableFuture<Void>
     */
    private CompletableFuture<Void> processEventThumbnailAsync(EventDetailVO eventDetailVO) {
        return CompletableFuture.runAsync(() -> {
            try {
                // 业务层面的数据验证
                if (eventDetailVO == null) {
                    log.debug("事件详情对象为空，跳过缩略图处理");
                    return;
                }

                // 检查是否需要处理缩略图（业务决策）
                if (!shouldProcessThumbnail(eventDetailVO)) {
                    return;
                }

                // 委托给缩略图处理器进行技术处理
                String thumbnailUrl = thumbnailProcessor.processThumbnail(
                        eventDetailVO.getDiscoveryImagePath(),
                        eventDetailVO.getBox(),
                        eventDetailVO.getId()
                );

                // 业务逻辑：根据处理结果更新事件详情
                if (thumbnailUrl != null) {
                    // 将缩略图URL设置到discoveryImagePath字段，替换原始图片URL
                    eventDetailVO.setDiscoveryImagePath(thumbnailUrl);
                    log.debug("事件ID: {} 已更新为缩略图路径", eventDetailVO.getId());
                }
                // 如果缩略图处理失败，保持原始图片路径不变

            } catch (Exception e) {
                log.error("处理事件缩略图时发生异常，事件ID: {}, 错误信息: {}",
                         eventDetailVO != null ? eventDetailVO.getId() : "null", e.getMessage(), e);
                // 异常情况下不影响主流程，继续使用原始图片
            }
        });
    }

    /**
     * 处理事件缩略图生成（保留原有同步方法作为备用）
     * 根据业务需求决定是否生成缩略图，并更新事件详情中的图片路径
     *
     * @param eventDetailVO 事件详情VO对象
     */
	@Deprecated
    private void processEventThumbnail(EventDetailVO eventDetailVO) {
        try {
            // 业务层面的数据验证
            if (eventDetailVO == null) {
                log.debug("事件详情对象为空，跳过缩略图处理");
                return;
            }

            // 检查是否需要处理缩略图（业务决策）
            if (!shouldProcessThumbnail(eventDetailVO)) {
                return;
            }

            // 委托给缩略图处理器进行技术处理
            String thumbnailUrl = thumbnailProcessor.processThumbnail(
                    eventDetailVO.getDiscoveryImagePath(),
                    eventDetailVO.getBox(),
                    eventDetailVO.getId()
            );

            // 业务逻辑：根据处理结果更新事件详情
            if (thumbnailUrl != null) {
                // 将缩略图URL设置到discoveryImagePath字段，替换原始图片URL
                eventDetailVO.setDiscoveryImagePath(thumbnailUrl);
                log.debug("事件ID: {} 已更新为缩略图路径", eventDetailVO.getId());
            }
            // 如果缩略图处理失败，保持原始图片路径不变

        } catch (Exception e) {
            log.error("处理事件缩略图时发生异常，事件ID: {}, 错误信息: {}",
                     eventDetailVO != null ? eventDetailVO.getId() : "null", e.getMessage(), e);
            // 异常情况下不影响主流程，继续使用原始图片
        }
    }

    /**
     * 判断是否应该处理缩略图（业务决策逻辑）
     *
     * @param eventDetailVO 事件详情VO对象
     * @return 是否应该处理缩略图
     */
    private boolean shouldProcessThumbnail(EventDetailVO eventDetailVO) {
        // 检查缩略图功能是否启用
        if (!thumbnailProcessor.isThumbnailEnabled()) {
            return false;
        }

        // 检查业务数据完整性
        if (!StringUtils.hasText(eventDetailVO.getDiscoveryImagePath())) {
            log.debug("事件ID: {} 缺少图片路径，跳过缩略图生成", eventDetailVO.getId());
            return false;
        }

        if (!StringUtils.hasText(eventDetailVO.getBox())) {
            log.debug("事件ID: {} 缺少bbox信息，跳过缩略图生成", eventDetailVO.getId());
            return false;
        }

        // 可以在这里添加更多业务规则，例如：
        // - 检查事件状态是否需要缩略图
        // - 检查用户权限是否允许查看缩略图
        // - 检查图片类型是否支持缩略图生成

        return true;
    }

    @Override
    public GeoJsonFeatureCollection getMapEventData(MapEventQueryDTO queryDTO) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<Event>()
                // 默认过滤已删除的记录
                .eq(Event::getIsDeleted, 0)
                // 排除位置信息为空的记录
                .isNotNull(Event::getLocation);

            // 处理bbox空间查询
            if (StringUtils.hasText(queryDTO.getBbox())) {
                try {
                    String[] coords = queryDTO.getBbox().split(",");
                    if (coords.length == 4) {
                        double minLng = Double.parseDouble(coords[0]);
                        double minLat = Double.parseDouble(coords[1]);
                        double maxLng = Double.parseDouble(coords[2]);
                        double maxLat = Double.parseDouble(coords[3]);

                        // 使用PostGIS的ST_Intersects函数进行空间查询
                        // 这里简化处理，实际应该使用空间索引查询
                        queryWrapper.apply("ST_Intersects(location, ST_MakeEnvelope({0}, {1}, {2}, {3}, 4326))",
                                          minLng, minLat, maxLng, maxLat);
                    }
                } catch (Exception e) {
                    log.error("解析bbox参数失败: {}", queryDTO.getBbox(), e);
                }
            }

            // 处理时间范围查询
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (StringUtils.hasText(queryDTO.getStartDate())) {
                try {
                    LocalDateTime startDateTime = LocalDateTime.parse(queryDTO.getStartDate(), formatter);
                    queryWrapper.ge(Event::getDiscoveryTime, Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant()));
                } catch (Exception e) {
                    log.error("解析开始时间失败: {}", queryDTO.getStartDate(), e);
                }
            }

            if (StringUtils.hasText(queryDTO.getEndDate())) {
                try {
                    LocalDateTime endDateTime = LocalDateTime.parse(queryDTO.getEndDate(), formatter);
                    queryWrapper.le(Event::getDiscoveryTime, Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant()));
                } catch (Exception e) {
                    log.error("解析结束时间失败: {}", queryDTO.getEndDate(), e);
                }
            }

            // 处理其他筛选条件
            if (queryDTO.getEventStatus() != null) {
                queryWrapper.eq(Event::getEventStatus, queryDTO.getEventStatus());
            }

            if (queryDTO.getWasteMaterial() != null) {
                queryWrapper.eq(Event::getWasteMaterial, queryDTO.getWasteMaterial());
            }

            // 查询事件列表
            List<Event> eventList = this.list(queryWrapper);

            // 转换为GeoJSON格式
            List<GeoJsonFeature> features = eventList.stream()
                .map(this::convertToGeoJsonFeature)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            return new GeoJsonFeatureCollection(features);

        } catch (Exception e) {
            log.error("获取地图事件数据失败", e);
            // 返回空的FeatureCollection而不是抛出异常
            return new GeoJsonFeatureCollection(new ArrayList<>());
        }
    }

    /**
     * 将Event对象转换为GeoJsonFeature对象
     * @param event 事件对象
     * @return GeoJsonFeature对象
     */
    private GeoJsonFeature convertToGeoJsonFeature(Event event) {
        if (event == null || event.getLocation() == null || event.getLocation().isEmpty()) {
            return null;
        }

        try {
            Point location = event.getLocation();

            // 创建几何对象
            PointGeometry geometry = new PointGeometry(location.getX(), location.getY());

            // 创建属性对象
            Map<String, Object> properties = new HashMap<>();
            properties.put("id", event.getId());
            properties.put("discoveryTime", event.getDiscoveryTime() != null ?
                event.getDiscoveryTime().toInstant().toString() : null);
            properties.put("wasteMaterial", event.getWasteMaterial());
            properties.put("wasteSize", event.getWasteSize());
            properties.put("eventStatus", event.getEventStatus());
            properties.put("confidence", event.getConfidence());

            // 处理图片路径，转换为真实URL
            if (StringUtils.hasText(event.getDiscoveryImagePath())) {
                String realImageUrl = fhMinioService.getObjectUrl(event.getDiscoveryImagePath());
                properties.put("discoveryImagePath", realImageUrl);
            } else {
                properties.put("discoveryImagePath", null);
            }

            return new GeoJsonFeature(geometry, properties);

        } catch (Exception e) {
            log.error("转换事件为GeoJsonFeature失败，事件ID: {}", event.getId(), e);
            return null;
        }
    }

}
