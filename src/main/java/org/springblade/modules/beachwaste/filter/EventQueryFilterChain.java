package org.springblade.modules.beachwaste.filter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springblade.modules.beachwaste.filter.impl.*;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springframework.stereotype.Component;

/**
 * 事件查询过滤器链
 * 负责构建和管理过滤器责任链
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class EventQueryFilterChain {

    private final BboxFilter bboxFilter;
    private final DateRangeFilter dateRangeFilter;
    private final EventStatusFilter eventStatusFilter;
    private final WasteMaterialFilter wasteMaterialFilter;
    private final DiscoveryMethodFilter discoveryMethodFilter;
    private final GridIdFilter gridIdFilter;
    private final HandlerStaffFilter handlerStaffFilter;

    /**
     * 构建过滤器链并应用过滤条件
     *
     * @param queryWrapper 查询条件包装器
     * @param queryDTO 查询参数DTO
     */
    public void applyFilters(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO) {
        // 构建责任链
        EventQueryFilter filterChain = buildFilterChain();

        // 应用过滤器链
        filterChain.applyFilter(queryWrapper, queryDTO);
    }

    /**
     * 构建过滤器责任链
     * 可以根据需要调整过滤器的顺序
     *
     * @return 过滤器链的头节点
     */
    private EventQueryFilter buildFilterChain() {
        // 按照逻辑顺序构建责任链
        // 1. 地理边界框过滤（空间查询，通常最先执行以减少数据量）
        // 2. 日期范围过滤（时间范围过滤，基础条件）
        // 3. 事件状态过滤
        // 4. 垃圾材质过滤
        // 5. 发现方式过滤
        // 6. 网格ID过滤
        // 7. 处理人员过滤
        bboxFilter
            .setNext(dateRangeFilter)
            .setNext(eventStatusFilter)
            .setNext(wasteMaterialFilter)
            .setNext(discoveryMethodFilter)
            .setNext(gridIdFilter)
            .setNext(handlerStaffFilter);

        return bboxFilter;
    }

    /**
     * 获取过滤器链的统计信息
     * 用于调试和监控
     *
     * @return 过滤器数量
     */
    public int getFilterCount() {
        return 7; // 当前有7个过滤器
    }
}
