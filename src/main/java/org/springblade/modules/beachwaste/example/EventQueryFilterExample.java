package org.springblade.modules.beachwaste.example;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.filter.EventQueryFilterChain;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springframework.stereotype.Component;

/**
 * 事件查询过滤器使用示例
 * 展示如何使用责任链模式替代复杂的if-else嵌套
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EventQueryFilterExample {
    
    private final EventQueryFilterChain eventQueryFilterChain;
    
    /**
     * 旧的实现方式 - 大量if-else嵌套
     */
    public LambdaQueryWrapper<Event> buildQueryWrapperOldWay(EventMapQueryDTO queryDTO) {
        LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<>();
        
        // 基础条件
        queryWrapper.isNotNull(Event::getLocation);
        
        // 大量的if-else嵌套
        if (queryDTO.getBbox() != null && !queryDTO.getBbox().trim().isEmpty()) {
            // bbox处理逻辑...
            log.debug("处理bbox条件");
        }
        
        if (queryDTO.getStartDate() != null && !queryDTO.getStartDate().trim().isEmpty()) {
            // 开始日期处理逻辑...
            log.debug("处理开始日期条件");
        }
        
        if (queryDTO.getEndDate() != null && !queryDTO.getEndDate().trim().isEmpty()) {
            // 结束日期处理逻辑...
            log.debug("处理结束日期条件");
        }
        
        if (queryDTO.getEventStatus() != null) {
            queryWrapper.eq(Event::getEventStatus, queryDTO.getEventStatus());
            log.debug("处理事件状态条件");
        }
        
        if (queryDTO.getWasteMaterial() != null) {
            queryWrapper.eq(Event::getWasteMaterial, queryDTO.getWasteMaterial());
            log.debug("处理垃圾材质条件");
        }
        
        if (queryDTO.getDiscoveryMethod() != null) {
            queryWrapper.eq(Event::getDiscoveryMethod, queryDTO.getDiscoveryMethod().longValue());
            log.debug("处理发现方式条件");
        }
        
        if (queryDTO.getGridId() != null) {
            queryWrapper.eq(Event::getGridId, queryDTO.getGridId());
            log.debug("处理网格ID条件");
        }
        
        if (queryDTO.getHandlerStaffId() != null) {
            queryWrapper.eq(Event::getHandlerStaffId, queryDTO.getHandlerStaffId());
            log.debug("处理处理人员条件");
        }
        
        return queryWrapper;
    }
    
    /**
     * 新的实现方式 - 使用责任链模式
     */
    public LambdaQueryWrapper<Event> buildQueryWrapperNewWay(EventMapQueryDTO queryDTO) {
        LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<>();
        
        // 基础条件
        queryWrapper.isNotNull(Event::getLocation);
        
        // 使用责任链模式应用过滤条件 - 简洁明了
        eventQueryFilterChain.applyFilters(queryWrapper, queryDTO);
        
        return queryWrapper;
    }
    
    /**
     * 对比两种实现方式
     */
    public void compareImplementations() {
        EventMapQueryDTO queryDTO = createSampleQueryDTO();
        
        log.info("=== 对比两种实现方式 ===");
        
        // 旧方式
        long startTime = System.currentTimeMillis();
        LambdaQueryWrapper<Event> oldWrapper = buildQueryWrapperOldWay(queryDTO);
        long oldTime = System.currentTimeMillis() - startTime;
        log.info("旧方式执行时间: {}ms", oldTime);
        
        // 新方式
        startTime = System.currentTimeMillis();
        LambdaQueryWrapper<Event> newWrapper = buildQueryWrapperNewWay(queryDTO);
        long newTime = System.currentTimeMillis() - startTime;
        log.info("新方式执行时间: {}ms", newTime);
        
        log.info("责任链模式的优势:");
        log.info("1. 代码更简洁，消除了大量if-else嵌套");
        log.info("2. 每个过滤器职责单一，易于维护");
        log.info("3. 可以轻松添加新的过滤器");
        log.info("4. 过滤器可以独立测试");
        log.info("5. 符合开闭原则，扩展性好");
    }
    
    /**
     * 创建示例查询DTO
     */
    private EventMapQueryDTO createSampleQueryDTO() {
        EventMapQueryDTO queryDTO = new EventMapQueryDTO();
        queryDTO.setBbox("120.0,31.0,121.0,32.0");
        queryDTO.setStartDate("2023-10-01 00:00:00");
        queryDTO.setEndDate("2023-10-31 23:59:59");
        queryDTO.setEventStatus(1L);
        queryDTO.setWasteMaterial(2L);
        queryDTO.setDiscoveryMethod(0);
        queryDTO.setGridId(1L);
        queryDTO.setHandlerStaffId(1L);
        return queryDTO;
    }
}
