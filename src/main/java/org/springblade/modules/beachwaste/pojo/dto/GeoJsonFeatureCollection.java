package org.springblade.modules.beachwaste.pojo.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * GeoJSON FeatureCollection对象
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GeoJsonFeatureCollection {
    
    /**
     * 类型，固定为"FeatureCollection"
     */
    private final String type = "FeatureCollection";
    
    /**
     * Feature列表
     */
    private List<GeoJsonFeature> features;
}