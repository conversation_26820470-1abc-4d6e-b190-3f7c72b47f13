package org.springblade.modules.beachwaste.filter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;

/**
 * 事件查询过滤器接口
 * 使用责任链模式处理查询条件
 * 
 * <AUTHOR>
 */
public interface EventQueryFilter {
    
    /**
     * 设置下一个过滤器
     * 
     * @param nextFilter 下一个过滤器
     * @return 当前过滤器实例，支持链式调用
     */
    EventQueryFilter setNext(EventQueryFilter nextFilter);
    
    /**
     * 应用过滤条件
     * 
     * @param queryWrapper 查询条件包装器
     * @param queryDTO 查询参数DTO
     */
    void applyFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO);
}
