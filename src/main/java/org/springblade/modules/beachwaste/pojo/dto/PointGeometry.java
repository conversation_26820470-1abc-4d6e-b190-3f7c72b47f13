package org.springblade.modules.beachwaste.pojo.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * GeoJSON Point几何对象
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PointGeometry {
    
    /**
     * 几何类型，固定为"Point"
     */
    private final String type = "Point";
    
    /**
     * 坐标数组 [经度, 纬度]
     */
    private double[] coordinates;
    
    /**
     * 构造函数
     * @param longitude 经度
     * @param latitude 纬度
     */
    public PointGeometry(double longitude, double latitude) {
        this.coordinates = new double[]{longitude, latitude};
    }
}