package org.springblade.modules.beachwaste.filter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springblade.modules.beachwaste.filter.impl.*;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 事件查询过滤器链测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class EventQueryFilterChainTest {
    
    @Mock
    private BboxFilter bboxFilter;
    
    @Mock
    private DateRangeFilter dateRangeFilter;
    
    @Mock
    private EventStatusFilter eventStatusFilter;
    
    @Mock
    private WasteMaterialFilter wasteMaterialFilter;
    
    @Mock
    private DiscoveryMethodFilter discoveryMethodFilter;
    
    @Mock
    private GridIdFilter gridIdFilter;
    
    @Mock
    private HandlerStaffFilter handlerStaffFilter;
    
    @InjectMocks
    private EventQueryFilterChain eventQueryFilterChain;
    
    private LambdaQueryWrapper<Event> queryWrapper;
    private EventMapQueryDTO queryDTO;
    
    @BeforeEach
    void setUp() {
        queryWrapper = new LambdaQueryWrapper<>();
        queryDTO = new EventMapQueryDTO();
    }
    
    @Test
    void testGetFilterCount() {
        // 测试过滤器数量
        assertEquals(7, eventQueryFilterChain.getFilterCount());
    }
    
    @Test
    void testApplyFiltersWithEmptyQueryDTO() {
        // 测试空查询条件
        assertDoesNotThrow(() -> {
            eventQueryFilterChain.applyFilters(queryWrapper, queryDTO);
        });
    }
    
    @Test
    void testApplyFiltersWithFullQueryDTO() {
        // 设置完整的查询条件
        queryDTO.setBbox("120.0,31.0,121.0,32.0");
        queryDTO.setStartDate("2023-10-01 00:00:00");
        queryDTO.setEndDate("2023-10-31 23:59:59");
        queryDTO.setEventStatus(1L);
        queryDTO.setWasteMaterial(2L);
        queryDTO.setDiscoveryMethod(0);
        queryDTO.setGridId(1L);
        queryDTO.setHandlerStaffId(1L);
        
        // 测试应用过滤器链
        assertDoesNotThrow(() -> {
            eventQueryFilterChain.applyFilters(queryWrapper, queryDTO);
        });
    }
}
