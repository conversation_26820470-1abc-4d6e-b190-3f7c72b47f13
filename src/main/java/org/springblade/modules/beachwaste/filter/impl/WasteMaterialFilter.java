package org.springblade.modules.beachwaste.filter.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.filter.AbstractEventQueryFilter;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springframework.stereotype.Component;

/**
 * 垃圾材质过滤器
 * 处理垃圾材质类型的过滤条件
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class WasteMaterialFilter extends AbstractEventQueryFilter {
    
    @Override
    protected void doFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO) {
        if (queryDTO.getWasteMaterial() != null) {
            queryWrapper.eq(Event::getWasteMaterial, queryDTO.getWasteMaterial());
            log.debug("应用垃圾材质过滤条件: {}", queryDTO.getWasteMaterial());
        }
    }
}
