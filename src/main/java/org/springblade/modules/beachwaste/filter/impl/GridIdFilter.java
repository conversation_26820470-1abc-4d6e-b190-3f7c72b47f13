package org.springblade.modules.beachwaste.filter.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.filter.AbstractEventQueryFilter;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springframework.stereotype.Component;

/**
 * 网格ID过滤器
 * 处理网格ID的过滤条件
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class GridIdFilter extends AbstractEventQueryFilter {
    
    @Override
    protected void doFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO) {
        if (queryDTO.getGridId() != null) {
            queryWrapper.eq(Event::getGridId, queryDTO.getGridId());
            log.debug("应用网格ID过滤条件: {}", queryDTO.getGridId());
        }
    }
}
