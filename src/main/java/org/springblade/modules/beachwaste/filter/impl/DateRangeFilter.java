package org.springblade.modules.beachwaste.filter.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.filter.AbstractEventQueryFilter;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 日期范围过滤器
 * 处理开始日期和结束日期的过滤条件
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DateRangeFilter extends AbstractEventQueryFilter {
    
    @Override
    protected void doFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO) {
        // 处理开始日期
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            try {
                LocalDateTime startDateTime = parseDateTime(queryDTO.getStartDate());
                Date startDate = Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
                queryWrapper.ge(Event::getDiscoveryTime, startDate);
                log.debug("应用开始日期过滤条件: {}", queryDTO.getStartDate());
            } catch (Exception e) {
                log.error("开始日期格式错误: {}", queryDTO.getStartDate(), e);
            }
        }
        
        // 处理结束日期
        if (StringUtils.hasText(queryDTO.getEndDate())) {
            try {
                LocalDateTime endDateTime = parseDateTime(queryDTO.getEndDate());
                Date endDate = Date.from(endDateTime.atZone(ZoneId.systemDefault()).toInstant());
                queryWrapper.le(Event::getDiscoveryTime, endDate);
                log.debug("应用结束日期过滤条件: {}", queryDTO.getEndDate());
            } catch (Exception e) {
                log.error("结束日期格式错误: {}", queryDTO.getEndDate(), e);
            }
        }
    }
    
    /**
     * 解析日期时间字符串
     * 支持格式：yyyy-MM-dd HH:mm:ss 或 yyyy-MM-dd
     * 
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime对象
     */
    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr.length() > 10) {
            // 包含时间部分：yyyy-MM-dd HH:mm:ss
            LocalDate date = LocalDate.parse(dateTimeStr.substring(0, 10));
            LocalTime time = LocalTime.parse(dateTimeStr.substring(11));
            return date.atTime(time);
        } else {
            // 只有日期部分：yyyy-MM-dd，默认使用当天开始时间
            return LocalDate.parse(dateTimeStr).atStartOfDay();
        }
    }
}
