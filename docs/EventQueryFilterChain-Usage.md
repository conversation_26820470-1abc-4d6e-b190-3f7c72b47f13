# 事件查询过滤器链使用说明

## 概述

事件查询过滤器链采用责任链设计模式，将复杂的查询条件处理逻辑分解为多个独立的过滤器，每个过滤器负责处理特定的查询条件。这种设计提高了代码的可读性、可维护性和可扩展性。

## 设计模式

### 责任链模式的优势

1. **单一职责原则**：每个过滤器只负责一种查询条件的处理
2. **开闭原则**：可以轻松添加新的过滤器而不修改现有代码
3. **可配置性**：可以动态调整过滤器的执行顺序
4. **可测试性**：每个过滤器可以独立测试

## 架构设计

```
EventQueryFilter (接口)
    ↑
AbstractEventQueryFilter (抽象基类)
    ↑
具体过滤器实现类
    ↓
EventQueryFilterChain (过滤器链管理器)
```

## 过滤器列表

| 过滤器 | 功能 | 处理字段 |
|--------|------|----------|
| BboxFilter | 地理边界框过滤 | bbox |
| DateRangeFilter | 日期范围过滤 | startDate, endDate |
| EventStatusFilter | 事件状态过滤 | eventStatus |
| WasteMaterialFilter | 垃圾材质过滤 | wasteMaterial |
| DiscoveryMethodFilter | 发现方式过滤 | discoveryMethod |
| GridIdFilter | 网格ID过滤 | gridId |
| HandlerStaffFilter | 处理人员过滤 | handlerStaffId |

## 使用方式

### 在Service中使用

```java
@Service
@AllArgsConstructor
public class EventServiceImpl implements IEventService {
    
    private final EventQueryFilterChain eventQueryFilterChain;
    
    public GeoJsonFeatureCollection getMapEvents(EventMapQueryDTO queryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<>();
        
        // 基础条件
        queryWrapper.isNotNull(Event::getLocation);
        
        // 使用责任链模式应用过滤条件
        eventQueryFilterChain.applyFilters(queryWrapper, queryDTO);
        
        // 执行查询
        List<Event> events = this.list(queryWrapper);
        
        // 处理结果...
    }
}
```

### 添加新的过滤器

1. 创建新的过滤器类：

```java
@Component
public class NewFilter extends AbstractEventQueryFilter {
    
    @Override
    protected void doFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO) {
        if (queryDTO.getNewField() != null) {
            queryWrapper.eq(Event::getNewField, queryDTO.getNewField());
            log.debug("应用新字段过滤条件: {}", queryDTO.getNewField());
        }
    }
}
```

2. 在EventQueryFilterChain中注册：

```java
@Component
@RequiredArgsConstructor
public class EventQueryFilterChain {
    
    private final NewFilter newFilter;
    // ... 其他过滤器
    
    private EventQueryFilter buildFilterChain() {
        // 在责任链中添加新过滤器
        bboxFilter
            .setNext(dateRangeFilter)
            .setNext(newFilter)  // 添加新过滤器
            .setNext(eventStatusFilter)
            // ... 其他过滤器
    }
}
```

## 性能优化建议

1. **过滤器顺序**：将能够最大程度减少数据量的过滤器放在前面
2. **空间查询优先**：BboxFilter放在最前面，利用空间索引快速过滤
3. **时间范围其次**：DateRangeFilter紧随其后，时间索引通常效率较高
4. **业务条件最后**：其他业务条件按照选择性从高到低排序

## 扩展性

### 支持不同的DTO类型

可以通过泛型扩展支持不同的查询DTO：

```java
public interface EventQueryFilter<T> {
    EventQueryFilter<T> setNext(EventQueryFilter<T> nextFilter);
    void applyFilter(LambdaQueryWrapper<Event> queryWrapper, T queryDTO);
}
```

### 支持条件组合

可以扩展支持AND/OR等逻辑组合：

```java
public class CompositeFilter extends AbstractEventQueryFilter {
    private final List<EventQueryFilter> filters;
    private final LogicalOperator operator; // AND, OR
    
    // 实现复合条件逻辑
}
```

## 测试

每个过滤器都应该有对应的单元测试：

```java
@Test
void testDateRangeFilter() {
    DateRangeFilter filter = new DateRangeFilter();
    LambdaQueryWrapper<Event> queryWrapper = new LambdaQueryWrapper<>();
    EventMapQueryDTO queryDTO = new EventMapQueryDTO();
    queryDTO.setStartDate("2023-10-01 00:00:00");
    queryDTO.setEndDate("2023-10-31 23:59:59");
    
    filter.doFilter(queryWrapper, queryDTO);
    
    // 验证查询条件是否正确添加
    assertNotNull(queryWrapper.getExpression());
}
```

## 监控和调试

可以通过日志监控过滤器的执行：

```java
@Override
protected void doFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO) {
    if (queryDTO.getEventStatus() != null) {
        queryWrapper.eq(Event::getEventStatus, queryDTO.getEventStatus());
        log.debug("应用事件状态过滤条件: {}", queryDTO.getEventStatus());
    }
}
```

## 总结

责任链模式的引入显著提升了代码质量：

- **消除了大量的if-else嵌套**
- **提高了代码的可读性和可维护性**
- **增强了系统的可扩展性**
- **便于单元测试和调试**
- **符合SOLID设计原则**
