package org.springblade.modules.beachwaste.filter.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.filter.AbstractEventQueryFilter;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springframework.stereotype.Component;

/**
 * 处理人员过滤器
 * 处理处理人员ID的过滤条件
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class HandlerStaffFilter extends AbstractEventQueryFilter {
    
    @Override
    protected void doFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO) {
        if (queryDTO.getHandlerStaffId() != null) {
            queryWrapper.eq(Event::getHandlerStaffId, queryDTO.getHandlerStaffId());
            log.debug("应用处理人员过滤条件: {}", queryDTO.getHandlerStaffId());
        }
    }
}
