package org.springblade.modules.beachwaste.filter.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.filter.AbstractEventQueryFilter;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springframework.stereotype.Component;

/**
 * 发现方式过滤器
 * 处理发现方式的过滤条件
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DiscoveryMethodFilter extends AbstractEventQueryFilter {
    
    @Override
    protected void doFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO) {
        if (queryDTO.getDiscoveryMethod() != null) {
            queryWrapper.eq(Event::getDiscoveryMethod, queryDTO.getDiscoveryMethod().longValue());
            log.debug("应用发现方式过滤条件: {}", queryDTO.getDiscoveryMethod());
        }
    }
}
