package org.springblade.modules.beachwaste.filter.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.filter.AbstractEventQueryFilter;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 地理边界框过滤器
 * 处理地图可视范围的空间过滤条件
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class BboxFilter extends AbstractEventQueryFilter {
    
    @Override
    protected void doFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO) {
        if (StringUtils.hasText(queryDTO.getBbox())) {
            try {
                // 解析bbox参数：minLng,minLat,maxLng,maxLat
                String[] coords = queryDTO.getBbox().split(",");
                if (coords.length == 4) {
                    double minLng = Double.parseDouble(coords[0]);
                    double minLat = Double.parseDouble(coords[1]);
                    double maxLng = Double.parseDouble(coords[2]);
                    double maxLat = Double.parseDouble(coords[3]);
                    
                    // 使用PostGIS的ST_MakeEnvelope函数创建边界框，并使用ST_Intersects进行空间查询
                    // 注意：这里使用原生SQL，因为MyBatis Plus不直接支持PostGIS函数
                    String bboxCondition = String.format(
                        "ST_Intersects(location, ST_MakeEnvelope(%f, %f, %f, %f, 4326))",
                        minLng, minLat, maxLng, maxLat
                    );
                    queryWrapper.apply(bboxCondition);
                    
                    log.debug("应用地理边界框过滤条件: {}", queryDTO.getBbox());
                } else {
                    log.warn("bbox参数格式错误，应为：minLng,minLat,maxLng,maxLat，实际值：{}", queryDTO.getBbox());
                }
            } catch (NumberFormatException e) {
                log.error("bbox参数解析失败: {}", queryDTO.getBbox(), e);
            }
        }
    }
}
