package org.springblade.modules.beachwaste.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.PrecisionModel;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.GeoJsonFeature;
import org.springblade.modules.beachwaste.pojo.dto.GeoJsonFeatureCollection;
import org.springblade.modules.beachwaste.pojo.dto.PointGeometry;
import org.springblade.modules.beachwaste.pojo.entity.Event;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 地图事件服务测试类
 * 验证地图事件点数据API的核心逻辑
 *
 * <AUTHOR>
class EventMapServiceTest {

    private GeometryFactory geometryFactory;

    @BeforeEach
    void setUp() {
        geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);
    }

    @Test
    void testEventMapQueryDTOValidation() {
        // 测试EventMapQueryDTO的参数验证
        EventMapQueryDTO queryDTO = new EventMapQueryDTO();

        // 设置有效的bbox参数
        queryDTO.setBbox("120.0,31.0,121.0,32.0");
        assertEquals("120.0,31.0,121.0,32.0", queryDTO.getBbox());

        // 设置时间范围
        queryDTO.setStartDate("2023-10-01 00:00:00");
        queryDTO.setEndDate("2023-10-31 23:59:59");
        assertEquals("2023-10-01 00:00:00", queryDTO.getStartDate());
        assertEquals("2023-10-31 23:59:59", queryDTO.getEndDate());

        // 设置其他过滤条件
        queryDTO.setEventStatus(1L);
        queryDTO.setWasteMaterial(2L);
        queryDTO.setDiscoveryMethod(0);
        queryDTO.setGridId(1L);
        queryDTO.setHandlerStaffId(1L);

        assertNotNull(queryDTO.getEventStatus());
        assertNotNull(queryDTO.getWasteMaterial());
        assertNotNull(queryDTO.getDiscoveryMethod());
        assertNotNull(queryDTO.getGridId());
        assertNotNull(queryDTO.getHandlerStaffId());
    }

    @Test
    void testGeoJsonFeatureCreation() {
        // 测试GeoJSON Feature对象的创建

        // 创建Point几何对象
        double longitude = 121.4737;
        double latitude = 31.2304;
        PointGeometry geometry = new PointGeometry(longitude, latitude);

        // 验证坐标
        assertNotNull(geometry.getCoordinates());
        assertEquals(2, geometry.getCoordinates().length);
        assertEquals(longitude, geometry.getCoordinates()[0]);
        assertEquals(latitude, geometry.getCoordinates()[1]);
        assertEquals("Point", geometry.getType());

        // 创建属性对象
        Map<String, Object> properties = new HashMap<>();
        properties.put("id", 1001L);
        properties.put("discoveryTime", "2023-10-26T10:30:00Z");
        properties.put("wasteMaterial", 1L);
        properties.put("wasteSize", 2L);
        properties.put("eventStatus", 1L);
        properties.put("confidence", 95.50);
        properties.put("discoveryImagePath", "/images/discovery/abc.jpg");

        // 创建GeoJSON Feature
        GeoJsonFeature feature = new GeoJsonFeature(geometry, properties);

        assertNotNull(feature);
        assertEquals("Feature", feature.getType());
        assertEquals(geometry, feature.getGeometry());
        assertEquals(properties, feature.getProperties());
        assertEquals(1001L, feature.getProperties().get("id"));
    }

    @Test
    void testGeoJsonFeatureCollectionCreation() {
        // 测试GeoJSON FeatureCollection的创建

        // 创建多个Feature
        GeoJsonFeature feature1 = createTestFeature(121.4737, 31.2304, 1001L);
        GeoJsonFeature feature2 = createTestFeature(121.4800, 31.2400, 1002L);

        // 创建FeatureCollection
        GeoJsonFeatureCollection collection = new GeoJsonFeatureCollection();
        collection.setFeatures(java.util.List.of(feature1, feature2));

        assertNotNull(collection);
        assertEquals("FeatureCollection", collection.getType());
        assertNotNull(collection.getFeatures());
        assertEquals(2, collection.getFeatures().size());

        // 验证第一个Feature
        GeoJsonFeature firstFeature = collection.getFeatures().get(0);
        assertEquals(1001L, firstFeature.getProperties().get("id"));
        assertEquals(121.4737, firstFeature.getGeometry().getCoordinates()[0]);
        assertEquals(31.2304, firstFeature.getGeometry().getCoordinates()[1]);
    }

    @Test
    void testEventToGeoJsonConversion() {
        // 测试Event对象到GeoJSON的转换逻辑

        // 创建测试Event对象
        Event event = createTestEvent();

        // 模拟转换逻辑
        GeoJsonFeature feature = convertEventToGeoJsonFeature(event);

        assertNotNull(feature);
        assertEquals("Feature", feature.getType());

        // 验证几何信息
        PointGeometry geometry = feature.getGeometry();
        assertNotNull(geometry);
        assertEquals("Point", geometry.getType());
        assertEquals(121.4737, geometry.getCoordinates()[0]);
        assertEquals(31.2304, geometry.getCoordinates()[1]);

        // 验证属性信息
        Map<String, Object> properties = feature.getProperties();
        assertNotNull(properties);
        assertEquals(1001L, properties.get("id"));
        assertEquals(1L, properties.get("wasteMaterial"));
        assertEquals(2L, properties.get("wasteSize"));
        assertEquals(1L, properties.get("eventStatus"));
        assertEquals(new BigDecimal("95.50"), properties.get("confidence"));
        assertEquals("/images/discovery/test.jpg", properties.get("discoveryImagePath"));
    }

    @Test
    void testBboxParameterParsing() {
        // 测试bbox参数解析逻辑
        String bbox = "120.0,31.0,121.0,32.0";
        String[] coords = bbox.split(",");

        assertEquals(4, coords.length);
        assertEquals(120.0, Double.parseDouble(coords[0])); // minLng
        assertEquals(31.0, Double.parseDouble(coords[1]));  // minLat
        assertEquals(121.0, Double.parseDouble(coords[2])); // maxLng
        assertEquals(32.0, Double.parseDouble(coords[3]));  // maxLat
    }

    /**
     * 创建测试用的GeoJsonFeature
     */
    private GeoJsonFeature createTestFeature(double longitude, double latitude, Long id) {
        PointGeometry geometry = new PointGeometry(longitude, latitude);
        Map<String, Object> properties = new HashMap<>();
        properties.put("id", id);
        properties.put("eventStatus", 1L);
        properties.put("wasteMaterial", 1L);
        return new GeoJsonFeature(geometry, properties);
    }

    /**
     * 创建测试用的Event对象
     */
    private Event createTestEvent() {
        Event event = new Event();
        event.setId(1001L);
        event.setWasteMaterial(1L);
        event.setWasteSize(2L);
        event.setEventStatus(1L);
        event.setConfidence(new BigDecimal("95.50"));
        event.setDiscoveryImagePath("/images/discovery/test.jpg");
        event.setDiscoveryTime(new Date());
        event.setDiscoveryMethod(0L);
        event.setGridId(1L);

        // 创建Point位置
        Point location = geometryFactory.createPoint(new Coordinate(121.4737, 31.2304));
        event.setLocation(location);

        return event;
    }

    /**
     * 模拟Event到GeoJsonFeature的转换逻辑
     * 这个方法模拟了EventServiceImpl中的convertEventToGeoJsonFeature方法
     */
    private GeoJsonFeature convertEventToGeoJsonFeature(Event event) {
        if (event == null || event.getLocation() == null || event.getLocation().isEmpty()) {
            return null;
        }

        try {
            Point location = event.getLocation();

            // 创建几何对象
            double longitude = location.getX();
            double latitude = location.getY();
            PointGeometry geometry = new PointGeometry(longitude, latitude);

            // 创建属性对象
            Map<String, Object> properties = new HashMap<>();
            properties.put("id", event.getId());
            properties.put("discoveryTime", event.getDiscoveryTime() != null ?
                event.getDiscoveryTime().toInstant().toString() : null);
            properties.put("wasteMaterial", event.getWasteMaterial());
            properties.put("wasteSize", event.getWasteSize());
            properties.put("eventStatus", event.getEventStatus());
            properties.put("confidence", event.getConfidence());
            properties.put("discoveryImagePath", event.getDiscoveryImagePath());
            properties.put("discoveryMethod", event.getDiscoveryMethod());
            properties.put("gridId", event.getGridId());

            return new GeoJsonFeature(geometry, properties);

        } catch (Exception e) {
            return null;
        }
    }
}
