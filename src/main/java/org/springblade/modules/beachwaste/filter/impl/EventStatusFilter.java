package org.springblade.modules.beachwaste.filter.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.filter.AbstractEventQueryFilter;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springframework.stereotype.Component;

/**
 * 事件状态过滤器
 * 处理事件状态的过滤条件
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class EventStatusFilter extends AbstractEventQueryFilter {
    
    @Override
    protected void doFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO) {
        if (queryDTO.getEventStatus() != null) {
            queryWrapper.eq(Event::getEventStatus, queryDTO.getEventStatus());
            log.debug("应用事件状态过滤条件: {}", queryDTO.getEventStatus());
        }
    }
}
