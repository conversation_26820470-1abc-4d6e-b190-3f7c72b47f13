package org.springblade.modules.beachwaste.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletResponse;
import org.springblade.core.tool.api.R;
import org.springblade.modules.beachwaste.pojo.dto.EventProcessDTO;
import org.springblade.modules.beachwaste.pojo.dto.EventQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.GeoJsonFeatureCollection;
import org.springblade.modules.beachwaste.pojo.dto.GridEventQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.MapEventQueryDTO;
import org.springblade.modules.beachwaste.pojo.dto.StaffEventQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;
import org.springblade.modules.beachwaste.pojo.vo.EventDetailVO;
import org.springblade.modules.beachwaste.pojo.vo.EventLocationVO;

import java.util.List;

/**
 * 事件信息服务接口
 *
 * <AUTHOR>
 */
public interface IEventService extends IService<Event> {

	/**
	 * 获取垃圾材质数量统计
	 * @param timeRange 时间范围（1：最近一个月，2：最近六个月，3：最近一年）
	 * @return 各类垃圾材质的数量统计
	 */
	R getWasteMaterialStats(Integer timeRange);

	/**
	 * 校验后保存事件信息
	 * @param event 事件实体对象
	 * @return 保存结果（成功或失败原因）
	 */
	R checkToSave(Event event);

	/**
	 * 校验后更新事件信息
	 * @param event 事件实体对象
	 * @return 更新结果（成功或失败原因）
	 */
	R checkToUpdateById(Event event);

	/**
	 * 根据条件查询事件列表
	 * @param discoveryMethod 发现方式ID（可选，null表示全部）
	 * @param eventStatus 事件状态ID（可选，null表示全部）
	 * @param gridId 网格ID（可选，null表示全部）
	 * @param startDate 开始日期（可选，格式：yyyy-MM-dd）
	 * @param endDate 结束日期（可选，格式：yyyy-MM-dd）
	 * @return 事件列表
	 */
	R listByCondition(Long discoveryMethod, Long eventStatus, Long gridId, String startDate, String endDate);

	/**
	 * 根据ID获取事件详情
	 * @param id 事件ID
	 * @return 事件详情VO
	 */
	EventDetailVO getEventDetailById(Long id);

	/**
	 * 导出事件详情到Excel
	 * @param ids 事件ID数组
	 * @param response HTTP响应对象
	 */
	void exportEventExcel(Long[] ids, HttpServletResponse response);

	/**
	 * 获取所有事件发生的年份列表
	 * @return 年份列表，按照降序排序
	 */
	R getEventYears();

	/**
	 * 获取指定年份各月份事件数量统计
	 * @param year 年份
	 * @return 各月份事件数量统计
	 */
	R getMonthlyStats(Integer year);

	/**
     * 根据网格ID查询相关事件列表
     * @param queryDTO 查询条件
     * @return 事件列表
     */
    R getEventsByGridId(GridEventQueryDTO queryDTO);

    /**
     * 根据时间范围获取事件位置信息
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 事件位置信息列表
     */
    List<EventLocationVO> getEventLocationsByDateRange(String startDate, String endDate);

    /**
     * 根据查询条件分页查询事件列表
     * @param queryDTO 查询条件
     * @return 分页事件列表
     */
    R listByQueryDTO(EventQueryDTO queryDTO);

    /**
     * 根据查询条件获取所有事件列表（不分页）
     * 用于高效查询全部数据
     *
     * @param queryDTO 查询条件
     * @return 所有符合条件的事件列表
     */
    R listAllByQueryDTO(EventQueryDTO queryDTO);

    /**
     * 获取指定年份已处置事件的处理人员统计
     * @param year 年份
     * @return 处理人员及其处理事件数量，从高到低排序
     */
    R getProcessedStaffStats(Integer year);

    /**
     * 获取当前网格员关联的事件列表
     * @param queryDTO 查询条件
     * @return 事件列表
     */
    R getEventsByStaffId(StaffEventQueryDTO queryDTO);

    /**
     * 根据时间范围导出事件Excel文件
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @param response HTTP响应对象
     */
    void exportEventsByDateRange(String startDate, String endDate, HttpServletResponse response);

    /**
     * 根据条件导出事件列表Excel文件
     * @param discoveryMethod 发现方式ID（可选，null表示全部）
     * @param eventStatus 事件状态ID（可选，null表示全部）
     * @param gridId 网格ID（可选，null表示全部）
     * @param startDate 开始日期（可选，格式：yyyy-MM-dd）
     * @param endDate 结束日期（可选，格式：yyyy-MM-dd）
     * @param response HTTP响应对象
     */
    void exportByCondition(Long discoveryMethod, Long eventStatus, Long gridId, String startDate, String endDate, HttpServletResponse response);

    /**
     * 获取当前登录用户的事件处理统计数据
     * @return 用户事件统计结果
     */
    R getUserEventStats();

    /**
     * 处置事件
     * @param processDTO 事件处置请求参数
     * @return 处置结果
     */
    R processEvent(EventProcessDTO processDTO);

    /**
     * 获取地图事件点位数据
     * 返回符合GeoJSON规范的事件点数据，供前端地图聚类展示
     * @param queryDTO 查询条件
     * @return GeoJSON FeatureCollection格式的事件点数据
     */
    GeoJsonFeatureCollection getMapEventData(MapEventQueryDTO queryDTO);

}
