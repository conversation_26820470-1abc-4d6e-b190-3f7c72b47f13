package org.springblade.modules.beachwaste.pojo.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * GeoJSON Feature对象
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GeoJsonFeature {
    
    /**
     * 类型，固定为"Feature"
     */
    private final String type = "Feature";
    
    /**
     * 几何信息
     */
    private PointGeometry geometry;
    
    /**
     * 属性信息
     */
    private Map<String, Object> properties;
}