package org.springblade.modules.beachwaste.filter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springblade.modules.beachwaste.pojo.dto.EventMapQueryDTO;
import org.springblade.modules.beachwaste.pojo.entity.Event;

/**
 * 事件查询过滤器抽象基类
 * 实现责任链模式的基础功能
 * 
 * <AUTHOR>
 */
public abstract class AbstractEventQueryFilter implements EventQueryFilter {
    
    /**
     * 下一个过滤器
     */
    protected EventQueryFilter nextFilter;
    
    @Override
    public EventQueryFilter setNext(EventQueryFilter nextFilter) {
        this.nextFilter = nextFilter;
        return nextFilter;
    }
    
    @Override
    public void applyFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO) {
        // 应用当前过滤器的逻辑
        doFilter(queryWrapper, queryDTO);
        
        // 如果有下一个过滤器，继续处理
        if (nextFilter != null) {
            nextFilter.applyFilter(queryWrapper, queryDTO);
        }
    }
    
    /**
     * 子类实现具体的过滤逻辑
     * 
     * @param queryWrapper 查询条件包装器
     * @param queryDTO 查询参数DTO
     */
    protected abstract void doFilter(LambdaQueryWrapper<Event> queryWrapper, EventMapQueryDTO queryDTO);
}
